// 全局要用的类型放到这里

declare global {
  interface IResData<T = any> {
    code: number
    msg: string
    data?: T
    result?: T
    [key: string]: any
  }

  // uni.uploadFile文件上传参数
  interface IUniUploadFileOptions {
    file?: File
    files?: UniApp.UploadFileOptionFiles[]
    filePath?: string
    name?: string
    formData?: any
  }

  interface IUserInfo {
    nickname?: string
    avatar?: string
    /** 微信的 openid，非微信没有这个字段 */
    openid?: string
    token?: string
  }

  /** 项目状态类型 */
  type ProjectStatus = '图像待提交' | 'AI审核待确认' | 'AI识别待确认' | '申诉待审批' | '申诉已驳回' | '申诉已通过' | '已结束'
    | '图像待提交' | 'AI识别中' | 'AI识别待确认' | '用户申诉' | '已结束'
    | '图像待提交, AI识别中'

  /** 项目状态类型（支持单个状态或逗号分隔的多个状态） */
  type ProjectStatusValue = ProjectStatus | string

  // 项目申诉记录表返回实体
  interface ProjectAppealRes {
    /* 申诉理由 */
    appealReason?: string
    /* 申诉时间 */
    appealTime?: string
    /* 申诉人 */
    applicantName?: string
    /* 附件Id */
    attachId?: string
    /* 创建人 */
    createBy?: string
    /* 创建时间 */
    createTime?: string
    /* 删除标志（0代表存在 2代表删除） */
    delFlag?: string
    /* 主键ID */
    id?: string
    /* 联系电话 */
    phone?: string
    /* 项目Id */
    projectId?: string
    /* 识别编号 */
    recognizeNo?: string
    /* 审核意见 */
    reviewComment?: string
    /* 审核结果 */
    reviewResult?: string
    /* 审核状态 */
    reviewStatus?: number
    /* 审核时间 */
    reviewTime?: string
    /* 更新人 */
    updateBy?: string
    /* 更新时间 */
    updateTime?: string
  }

  // 项目附件表返回实体
  interface ProjectAttachRes {
    /* 附件名 */
    attachName?: string
    /* 附件路径 */
    attachPath?: string
    /* 创建人 */
    createBy?: string
    /* 创建时间 */
    createTime?: string
    /* ID */
    id?: string
    /* 是否删除(0未删除，1已删除) */
    isDeleted?: number
    /* 项目id */
    projectId?: string
    /* 更新人 */
    updateBy?: string
    /* 更新时间 */
    updateTime?: string
    /* 上传人员 */
    uploadeBy?: string
    /* 上传时间 */
    uploadeTime?: string
  }

  // AI识别结果表返回实体
  interface ProjectRecognizeResultRes {
    /* 申诉判断结果 */
    appealResult?: string
    /* 外观 */
    appearance?: string
    /* 走线架层数 */
    cableRackLevel?: number
    /* 走线架长度(单位mm) */
    cableRackLong?: number
    /* 创建人 */
    createBy?: string
    /* 创建时间 */
    createTime?: string
    /* 龙骨间横担合格数量 */
    crossArmPassCount?: number
    /* 横担平均间距(单位mm) */
    crossArmSpacing?: number
    /* 龙骨间横担状态 */
    crossArmStatus?: string
    /* 接地不合格数量 */
    groundingFailCount?: number
    /* 接地合格数量 */
    groundingPassCount?: number
    /* 接地状态 */
    groundingStatus?: string
    /* 吊挂不合格数量 */
    hangerFailCount?: number
    /* 吊挂合格数量 */
    hangerPassCount?: number
    /* 吊挂平均间距（单位mm） */
    hangerSpacing?: number
    /* 吊挂状态 */
    hangerStatus?: string
    /* 主键ID */
    id?: string
    /* 龙骨状态 */
    keelStatus?: string
    /* 不规范点 */
    noQualified?: string
    /* 立柱合格数量 */
    pillarPassCount?: number
    /* 立柱平均间隔(单位mm) */
    pillarSpacing?: number
    /* 立柱状态 */
    pillarStatus?: string
    /* 关联项目ID */
    projectId?: string
    /* AI识别结果 */
    result?: string
    /* 更新人 */
    updateBy?: string
    /* 更新时间 */
    updateTime?: string
  }

  // AI识别详情表返回实体
  interface RecognizeDetailRes {
    /* 外观 */
    appearance?: string
    /* 附件ID */
    attachId?: string
    /* 附件路径 */
    attachPath?: string
    /* 走线架层数 */
    cableRackLevel?: number
    /* 走线架长度(单位mm) */
    cableRackLong?: number
    /* 创建人 */
    createdBy?: string
    /* 创建时间 */
    createdTime?: string
    /* 龙骨间横担合格数量 */
    crossArmPassCount?: number
    /* 横担平均间距(单位mm) */
    crossArmSpacing?: number
    /* 龙骨间横担状态 */
    crossArmStatus?: string
    /* 接地不合格数量 */
    groundingFailCount?: number
    /* 接地合格数量 */
    groundingPassCount?: number
    /* 接地状态 */
    groundingStatus?: string
    /* 吊挂不合格数量 */
    hangerFailCount?: number
    /* 吊挂合格数量 */
    hangerPassCount?: number
    /* 吊挂平均间距（单位mm） */
    hangerSpacing?: number
    /* 吊挂状态 */
    hangerStatus?: string
    /* 主键ID */
    id?: string
    /* 龙骨状态 */
    keelStatus?: string
    /* 立柱合格数量 */
    pillarPassCount?: number
    /* 立柱平均间隔(单位mm) */
    pillarSpacing?: number
    /* 立柱状态 */
    pillarStatus?: string
    /* 识别结果ID */
    recognizeId?: string
    /* 识别编号(文件名) */
    recognizeNo?: string
    /* 识别时间 */
    recognizeTime?: string
    /* 识别状态(完成，未完成) */
    status?: string
    /* 更新人 */
    updatedBy?: string
    /* 更新时间 */
    updatedTime?: string
    /* 上传人员 */
    uploadBy?: string
  }

  /* 项目详情响应实体 */
  interface ProjectDetailResponse {
    /* 用户申诉记录 */
    projectAppealResList?: ProjectAppealRes[]
    /* 项目附件 */
    projectAttachResList?: ProjectAttachRes[]
    /* 项目基本信息 */
    projectInfoRes?: ProjectInfoItem
    /* 整个项目的识别结果 */
    projectRecognizeResultRes?: ProjectRecognizeResultRes
    /* 单张图片的识别结果 */
    recognizeDetailResList?: RecognizeDetailRes[]
  }

  /* 项目信息接口 */
  interface ProjectInfoItem {
  /* 施工队长 */
    buildLeader?: string
    /* 施工单位 */
    buildUnit?: string
    /* 机房所属地市公司 */
    city?: string
    /* 地市机房建设负责人 */
    cityManager?: string
    /* 创建人 */
    createBy?: string
    /* 创建时间 */
    createTime?: string
    /* 区公司机房建设负责人 */
    districtManager?: string
    /* 收集完成时限 */
    finishTime?: string
    /* 框架名称 */
    frameName?: string
    /* 项目ID */
    id?: string
    /* 是否删除(0未删除，1已删除) */
    isDeleted?: number
    /* 项目状态 */
    projectStatus?: ProjectStatus
    /* 审核意见 */
    reviewOpinion?: string
    /* 审核结果 */
    reviewResult?: string
    /* 审核状态 */
    reviewStatus?: string
    /* 审核时间 */
    reviewTime?: string
    /* 机房地址 */
    roomAddress?: string
    /* 机房编号 */
    roomCode?: string
    /* 机房名称 */
    roomName?: string
    /* 机房类型 */
    roomType?: string
    /* 监理单位 */
    supervisionUnit?: string
    /* 监理员 */
    supervisor?: string
    /* 更新人 */
    updateBy?: string
    /* 更新时间 */
    updateTime?: string
  }

  // 通知类型定义
  /* 1任务通知; 2审批通知; 3结果通知 */
  type NoticeType = '1' | '2' | '3'
  /* 0正常; 1关闭 */
  type NoticeStatus = '0' | '1'
  /* 0未读; 1已读 */
  type NoticeReadStatus = '0' | '1'

  /* 通知项接口 */
  interface NoticeItem {
    /* 通知ID */
    noticeId?: number
    /* 通知标题 */
    noticeTitle?: string
    /* 通知内容 */
    noticeContent?: string
    /* 通知类型（1任务通知; 2审批通知; 3结果通知） */
    noticeType?: NoticeType
    /* 通知状态（0正常; 1关闭） */
    status?: NoticeStatus
    /* 是否已读（0未读; 1已读） */
    isRead?: NoticeReadStatus
    /* 工单id */
    projectId?: string
    /* 用户ID */
    userId?: number
    /* 创建者 */
    createBy?: string
    /* 创建时间 */
    createTime?: string
    /* 更新者 */
    updateBy?: string
    /* 更新时间 */
    updateTime?: string
  }
}

export {} // 防止模块污染
